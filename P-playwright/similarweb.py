#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
from datetime import datetime
from playwright.sync_api import sync_playwright
from urllib.parse import urlparse, parse_qs


class SimilarWebProSpider:
    """
    SimilarWeb Pro 网站分析工具
    用于模拟访问 SimilarWeb Pro 并获取网站分析数据
    """

    def __init__(self, headless=False, timeout=60000):
        """
        初始化爬虫

        Args:
            headless: 是否无头模式运行
            timeout: 页面超时时间（毫秒）
        """
        self.headless = headless
        self.timeout = timeout
        self.base_url = "https://pro.similarweb.com"

        # 创建必要的目录
        self.setup_directories()

    def setup_directories(self):
        """创建必要的目录"""
        directories = ['screenshots', 'downloads', 'html', 'results']
        for directory in directories:
            dir_path = os.path.join(os.path.dirname(__file__), directory)
            os.makedirs(dir_path, exist_ok=True)

    def load_cookies(self, cookie_file_path=None):
        """
        从文件加载cookies

        Args:
            cookie_file_path: cookie文件路径，默认为当前目录下的similarweb_cookies.json

        Returns:
            cookies列表或None
        """
        if cookie_file_path is None:
            cookie_file_path = os.path.join(os.path.dirname(__file__), 'similarweb_cookies.json')

        if not os.path.exists(cookie_file_path):
            print(f"Cookie文件不存在: {cookie_file_path}")
            return None

        try:
            with open(cookie_file_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)

            # 转换为Playwright格式
            cookies = []
            for name, value in cookies_data.items():
                cookies.append({
                    'name': name,
                    'value': str(value),
                    'domain': '.similarweb.com',
                    'path': '/'
                })

            print(f"成功加载 {len(cookies)} 个cookies")
            return cookies
        except Exception as e:
            print(f"加载cookies失败: {e}")
            return None

    def save_cookies(self, page, cookie_file_path=None):
        """
        保存当前页面的cookies到文件

        Args:
            page: Playwright页面对象
            cookie_file_path: 保存路径，默认为similarweb_cookies.json
        """
        if cookie_file_path is None:
            cookie_file_path = os.path.join(os.path.dirname(__file__), 'similarweb_cookies.json')

        try:
            cookies = page.context.cookies()
            cookies_dict = {}
            for cookie in cookies:
                cookies_dict[cookie['name']] = cookie['value']

            with open(cookie_file_path, 'w', encoding='utf-8') as f:
                json.dump(cookies_dict, f, indent=4, ensure_ascii=False)

            print(f"Cookies已保存到: {cookie_file_path}")
        except Exception as e:
            print(f"保存cookies失败: {e}")

    def parse_url_params(self, url):
        """
        解析SimilarWeb Pro URL参数

        Args:
            url: SimilarWeb Pro URL

        Returns:
            解析后的参数字典
        """
        try:
            # 解析URL
            parsed = urlparse(url)

            # 从fragment中提取参数
            fragment = parsed.fragment
            if not fragment:
                return {}

            # 解析路径参数
            path_parts = fragment.split('/')
            params = {}

            # 提取域名 (key参数)
            query_params = parse_qs(parsed.query) if parsed.query else {}
            if 'key' in query_params:
                params['domain'] = query_params['key'][0]

            # 从fragment路径中提取参数
            if len(path_parts) >= 6:
                params['country'] = path_parts[5] if path_parts[5] != '*' else '999'
                if len(path_parts) >= 7:
                    duration_part = path_parts[6].split('?')[0]
                    params['duration'] = duration_part

            # 解析查询参数
            if '?' in fragment:
                query_part = fragment.split('?')[1]
                query_params = parse_qs(query_part)
                for key, value in query_params.items():
                    if key not in params:
                        params[key] = value[0] if isinstance(value, list) and value else value

            return params
        except Exception as e:
            print(f"解析URL参数失败: {e}")
            return {}

    def take_screenshot(self, page, name_suffix=""):
        """
        截图保存

        Args:
            page: Playwright页面对象
            name_suffix: 文件名后缀

        Returns:
            截图文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"similarweb_{timestamp}{name_suffix}.png"
        screenshot_path = os.path.join(os.path.dirname(__file__), 'screenshots', filename)

        try:
            page.screenshot(path=screenshot_path, full_page=True)
            print(f"截图已保存: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            print(f"截图失败: {e}")
            return None

    def save_page_content(self, page, name_suffix=""):
        """
        保存页面HTML内容

        Args:
            page: Playwright页面对象
            name_suffix: 文件名后缀

        Returns:
            HTML文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"similarweb_{timestamp}{name_suffix}.html"
        html_path = os.path.join(os.path.dirname(__file__), 'html', filename)

        try:
            content = page.content()
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"页面内容已保存: {html_path}")
            return html_path
        except Exception as e:
            print(f"保存页面内容失败: {e}")
            return None

    def wait_for_page_load(self, page, max_wait_time=30):
        """
        等待页面完全加载

        Args:
            page: Playwright页面对象
            max_wait_time: 最大等待时间（秒）
        """
        try:
            # 等待网络空闲
            page.wait_for_load_state("networkidle", timeout=max_wait_time * 1000)

            # 额外等待一些时间确保动态内容加载
            time.sleep(3)

            print("页面加载完成")
        except Exception as e:
            print(f"等待页面加载超时: {e}")

    def extract_website_data(self, page):
        """
        从页面中提取网站分析数据

        Args:
            page: Playwright页面对象

        Returns:
            提取的数据字典
        """
        data = {
            'extracted_at': datetime.now().isoformat(),
            'url': page.url,
            'title': '',
            'metrics': {},
            'charts': {},
            'tables': {}
        }

        try:
            # 获取页面标题
            data['title'] = page.title()
            print(f"页面标题: {data['title']}")

            # 等待页面中的关键元素加载
            self.wait_for_elements(page)

            # 提取各种指标数据
            data['metrics'] = self.extract_metrics(page)
            data['charts'] = self.extract_chart_data(page)
            data['tables'] = self.extract_table_data(page)

        except Exception as e:
            print(f"提取数据时出错: {e}")

        return data

    def wait_for_elements(self, page):
        """等待关键元素加载"""
        try:
            # 等待主要内容区域
            page.wait_for_selector('[data-automation-id="main-content"]', timeout=10000)
        except:
            try:
                # 备选等待策略
                page.wait_for_selector('.app-content', timeout=10000)
            except:
                print("未找到主要内容区域，继续执行...")

    def extract_metrics(self, page):
        """提取指标数据"""
        metrics = {}

        try:
            # 尝试提取各种可能的指标
            metric_selectors = [
                '[data-automation-id="metric"]',
                '.metric-value',
                '.kpi-value',
                '.summary-metric',
                '[class*="metric"]'
            ]

            for selector in metric_selectors:
                elements = page.query_selector_all(selector)
                for i, element in enumerate(elements):
                    try:
                        text = element.inner_text().strip()
                        if text:
                            metrics[f'metric_{i}'] = text
                    except:
                        continue

                if metrics:  # 如果找到了指标就停止尝试其他选择器
                    break

            print(f"提取到 {len(metrics)} 个指标")

        except Exception as e:
            print(f"提取指标数据失败: {e}")

        return metrics

    def extract_chart_data(self, page):
        """提取图表数据"""
        charts = {}

        try:
            # 尝试提取图表相关的数据
            chart_selectors = [
                '[data-automation-id="chart"]',
                '.chart-container',
                '.highcharts-container',
                'svg[class*="chart"]'
            ]

            for selector in chart_selectors:
                elements = page.query_selector_all(selector)
                for i, element in enumerate(elements):
                    try:
                        # 获取图表的属性或文本
                        chart_info = {
                            'type': element.get_attribute('class') or 'unknown',
                            'data_attributes': {}
                        }

                        # 获取所有data-*属性
                        for attr in ['data-automation-id', 'data-testid', 'data-chart-type']:
                            value = element.get_attribute(attr)
                            if value:
                                chart_info['data_attributes'][attr] = value

                        charts[f'chart_{i}'] = chart_info
                    except:
                        continue

            print(f"找到 {len(charts)} 个图表")

        except Exception as e:
            print(f"提取图表数据失败: {e}")

        return charts

    def extract_table_data(self, page):
        """提取表格数据"""
        tables = {}

        try:
            # 查找表格
            table_elements = page.query_selector_all('table')

            for i, table in enumerate(table_elements):
                try:
                    rows = table.query_selector_all('tr')
                    table_data = []

                    for row in rows:
                        cells = row.query_selector_all('td, th')
                        row_data = [cell.inner_text().strip() for cell in cells]
                        if any(row_data):  # 只添加非空行
                            table_data.append(row_data)

                    if table_data:
                        tables[f'table_{i}'] = table_data

                except:
                    continue

            print(f"提取到 {len(tables)} 个表格")

        except Exception as e:
            print(f"提取表格数据失败: {e}")

        return tables

    def run(self, url, cookie_file=None, save_data=True):
        """
        运行爬虫访问指定URL

        Args:
            url: 要访问的SimilarWeb Pro URL
            cookie_file: cookie文件路径
            save_data: 是否保存提取的数据

        Returns:
            提取的数据字典
        """
        print(f"开始访问 {url}...")

        # 解析URL参数
        params = self.parse_url_params(url)
        print(f"解析的参数: {params}")

        # 提取域名
        domain = params.get('domain') or url.split('key=')[-1] if 'key=' in url else None
        if domain:
            print(f"目标域名: {domain}")

        # 加载cookies
        cookies = self.load_cookies(cookie_file)

        with sync_playwright() as p:
            try:
                # 启动浏览器
                print("正在启动浏览器...")
                browser = p.chromium.launch(
                    headless=self.headless,
                    channel="chrome" if not self.headless else None  # 在有头模式下使用已安装的Chrome
                )
                print("浏览器启动成功")

                # 创建浏览器上下文
                context_options = {
                    "viewport": {"width": 1920, "height": 1080},
                    "accept_downloads": True
                }

                context = browser.new_context(**context_options)

                # 设置cookies
                if cookies:
                    context.add_cookies(cookies)
                    print("已将cookies加载到浏览器")

                # 创建新页面
                page = context.new_page()

                # 设置超时时间
                page.set_default_timeout(self.timeout)

                # 访问目标URL
                print(f"正在加载页面: {url}")
                page.goto(url)
                print(f"页面已加载")

                # 等待页面加载完成
                self.wait_for_page_load(page)

                # 截图
                self.take_screenshot(page, f"_{domain or 'page'}")

                # 保存页面内容
                self.save_page_content(page, f"_{domain or 'page'}")

                # 提取数据
                print("开始提取数据...")
                data = self.extract_website_data(page)

                # 保存当前cookies
                self.save_cookies(page)

                # 保存提取的数据
                if save_data and data:
                    self.save_data(data, domain)

                print("数据提取完成")
                return data

            except Exception as e:
                print(f"运行过程中出错: {e}")
                return None
            finally:
                print("浏览器会话已结束")

    def save_data(self, data, domain=None):
        """
        保存提取的数据到JSON文件

        Args:
            data: 提取的数据
            domain: 域名（用于文件名）
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"similarweb_data_{domain or 'unknown'}_{timestamp}.json"
        file_path = os.path.join(os.path.dirname(__file__), 'results', filename)

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            print(f"数据已保存到: {file_path}")
            return file_path
        except Exception as e:
            print(f"保存数据失败: {e}")
            return None


def main():
    """主函数"""
    # 目标URL
    target_url = "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com"

    # 创建爬虫实例
    spider = SimilarWebProSpider(headless=False)

    # 运行爬虫
    data = spider.run(target_url)

    if data:
        print("成功获取数据")
    else:
        print("获取数据失败")


if __name__ == "__main__":
    main()