#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
from datetime import datetime
from playwright.sync_api import sync_playwright
from urllib.parse import urlparse, parse_qs


class SimilarWebProSpider:
    """
    SimilarWeb Pro 网站分析工具
    用于模拟访问 SimilarWeb Pro 并获取网站分析数据
    """

    def __init__(self, headless=False, timeout=60000):
        """
        初始化爬虫

        Args:
            headless: 是否无头模式运行
            timeout: 页面超时时间（毫秒）
        """
        self.headless = headless
        self.timeout = timeout
        self.base_url = "https://pro.similarweb.com"

        # 创建必要的目录
        self.setup_directories()

    def setup_directories(self):
        """创建必要的目录"""
        directories = ['screenshots', 'downloads', 'html', 'results']
        for directory in directories:
            dir_path = os.path.join(os.path.dirname(__file__), directory)
            os.makedirs(dir_path, exist_ok=True)

    def load_cookies(self, cookie_file_path=None):
        """
        从文件加载cookies，支持多种格式

        Args:
            cookie_file_path: cookie文件路径，默认为当前目录下的similarweb_cookies.json

        Returns:
            cookies列表或None
        """
        if cookie_file_path is None:
            cookie_file_path = os.path.join(os.path.dirname(__file__), 'similarweb_cookies.txt')

        if not os.path.exists(cookie_file_path):
            print(f"Cookie文件不存在: {cookie_file_path}")
            return None

        try:
            with open(cookie_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            cookies = []

            # 检查是否是浏览器cookie字符串格式 (name=value; name2=value2; ...)
            if not content.startswith('{') and ';' in content:
                print("检测到浏览器Cookie字符串格式")
                # 解析浏览器cookie字符串
                cookie_pairs = content.split(';')
                for pair in cookie_pairs:
                    pair = pair.strip()
                    if '=' in pair:
                        name, value = pair.split('=', 1)
                        cookies.append({
                            'name': name.strip(),
                            'value': value.strip(),
                            'domain': '.similarweb.com',
                            'path': '/'
                        })
            else:
                # JSON格式
                print("检测到JSON格式")
                cookies_data = json.loads(content)

                # 转换为Playwright格式
                for name, value in cookies_data.items():
                    # 跳过注释字段
                    if name.startswith('_'):
                        continue
                    cookies.append({
                        'name': name,
                        'value': str(value),
                        'domain': '.similarweb.com',
                        'path': '/'
                    })

            print(f"成功加载 {len(cookies)} 个cookies")
            return cookies
        except Exception as e:
            print(f"加载cookies失败: {e}")
            return None

    def save_cookies(self, page, cookie_file_path=None):
        """
        保存当前页面的cookies到文件

        Args:
            page: Playwright页面对象
            cookie_file_path: 保存路径，默认为similarweb_cookies.json
        """
        if cookie_file_path is None:
            cookie_file_path = os.path.join(os.path.dirname(__file__), 'similarweb_cookies.json')

        try:
            cookies = page.context.cookies()
            cookies_dict = {}
            for cookie in cookies:
                cookies_dict[cookie['name']] = cookie['value']

            with open(cookie_file_path, 'w', encoding='utf-8') as f:
                json.dump(cookies_dict, f, indent=4, ensure_ascii=False)

            print(f"Cookies已保存到: {cookie_file_path}")
        except Exception as e:
            print(f"保存cookies失败: {e}")

    def parse_url_params(self, url):
        """
        解析SimilarWeb Pro URL参数

        Args:
            url: SimilarWeb Pro URL

        Returns:
            解析后的参数字典
        """
        try:
            # 解析URL
            parsed = urlparse(url)

            # 从fragment中提取参数
            fragment = parsed.fragment
            if not fragment:
                return {}

            # 解析路径参数
            path_parts = fragment.split('/')
            params = {}

            # 提取域名 (key参数)
            query_params = parse_qs(parsed.query) if parsed.query else {}
            if 'key' in query_params:
                params['domain'] = query_params['key'][0]

            # 从fragment路径中提取参数
            # URL格式: #/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m
            # path_parts: ['', 'digitalsuite', 'websiteanalysis', 'overview', 'website-performance', '*', '999', '1m']
            if len(path_parts) >= 7:
                params['country'] = path_parts[6] if path_parts[6] != '*' else '999'
                if len(path_parts) >= 8:
                    duration_part = path_parts[7].split('?')[0]
                    params['duration'] = duration_part

            # 解析查询参数
            if '?' in fragment:
                query_part = fragment.split('?')[1]
                query_params = parse_qs(query_part)
                for key, value in query_params.items():
                    if key not in params:
                        params[key] = value[0] if isinstance(value, list) and value else value

            return params
        except Exception as e:
            print(f"解析URL参数失败: {e}")
            return {}

    def take_screenshot(self, page, name_suffix=""):
        """
        截图保存

        Args:
            page: Playwright页面对象
            name_suffix: 文件名后缀

        Returns:
            截图文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"similarweb_{timestamp}{name_suffix}.png"
        screenshot_path = os.path.join(os.path.dirname(__file__), 'screenshots', filename)

        try:
            page.screenshot(path=screenshot_path, full_page=True)
            print(f"截图已保存: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            print(f"截图失败: {e}")
            return None

    def save_page_content(self, page, name_suffix=""):
        """
        保存页面HTML内容

        Args:
            page: Playwright页面对象
            name_suffix: 文件名后缀

        Returns:
            HTML文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"similarweb_{timestamp}{name_suffix}.html"
        html_path = os.path.join(os.path.dirname(__file__), 'html', filename)

        try:
            content = page.content()
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"页面内容已保存: {html_path}")
            return html_path
        except Exception as e:
            print(f"保存页面内容失败: {e}")
            return None

    def wait_for_page_load(self, page, max_wait_time=60):
        """
        等待页面完全加载

        Args:
            page: Playwright页面对象
            max_wait_time: 最大等待时间（秒）
        """
        try:
            # 等待网络空闲
            page.wait_for_load_state("networkidle", timeout=max_wait_time * 1000)

            # 等待关键数据元素出现
            print("等待数据加载...")

            # 尝试等待一些关键的数据元素
            selectors_to_wait = [
                '[data-automation-id="overview-traffic"]',
                '.wa-overview__traffic-display',
                '.wa-overview__engagement-display',
                '.wa-overview__demographics-display',
                '[class*="traffic"]',
                '[class*="visits"]',
                '[class*="metric"]',
                '.js-overview-traffic-display'
            ]

            element_found = False
            for selector in selectors_to_wait:
                try:
                    page.wait_for_selector(selector, timeout=10000)
                    print(f"找到数据元素: {selector}")
                    element_found = True
                    break
                except:
                    continue

            if not element_found:
                print("未找到预期的数据元素，继续等待...")

            # 额外等待确保数据完全加载
            time.sleep(8)

            print("页面加载完成")
        except Exception as e:
            print(f"等待页面加载超时: {e}")

    def extract_website_data(self, page):
        """
        从页面中提取网站分析数据

        Args:
            page: Playwright页面对象

        Returns:
            提取的数据字典
        """
        data = {
            'extracted_at': datetime.now().isoformat(),
            'url': page.url,
            'title': '',
            'metrics': {},
            'charts': {},
            'tables': {}
        }

        try:
            # 获取页面标题
            data['title'] = page.title()
            print(f"页面标题: {data['title']}")

            # 等待页面中的关键元素加载
            self.wait_for_elements(page)

            # 提取各种指标数据
            data['metrics'] = self.extract_metrics(page)
            data['charts'] = self.extract_chart_data(page)
            data['tables'] = self.extract_table_data(page)
            data['visible_data'] = self.extract_all_visible_data(page)

        except Exception as e:
            print(f"提取数据时出错: {e}")

        return data

    def wait_for_elements(self, page):
        """等待关键元素加载"""
        try:
            # 等待主要内容区域
            page.wait_for_selector('[data-automation-id="main-content"]', timeout=10000)
        except:
            try:
                # 备选等待策略
                page.wait_for_selector('.app-content', timeout=10000)
            except:
                print("未找到主要内容区域，继续执行...")

    def extract_metrics(self, page):
        """提取指标数据"""
        metrics = {}

        try:
            print("开始提取访问量等关键指标...")

            # SimilarWeb Pro 的具体选择器
            specific_selectors = {
                # 总访问量相关
                'total_visits': [
                    '[data-automation-id="overview-traffic"] .wa-overview__traffic-display-value',
                    '.wa-overview__traffic-display .wa-overview__traffic-display-value',
                    '[class*="traffic-display-value"]',
                    '[class*="visits"] [class*="value"]',
                    '.js-overview-traffic-display-value'
                ],
                # 月访问量
                'monthly_visits': [
                    '[data-automation-id="monthly-visits"]',
                    '.wa-overview__monthly-visits',
                    '[class*="monthly-visits"]'
                ],
                # 跳出率
                'bounce_rate': [
                    '[data-automation-id="bounce-rate"]',
                    '.wa-overview__bounce-rate',
                    '[class*="bounce-rate"]'
                ],
                # 页面浏览量
                'page_views': [
                    '[data-automation-id="page-views"]',
                    '.wa-overview__page-views',
                    '[class*="page-views"]'
                ],
                # 平均访问时长
                'avg_visit_duration': [
                    '[data-automation-id="avg-visit-duration"]',
                    '.wa-overview__avg-visit-duration',
                    '[class*="visit-duration"]'
                ],
                # 每次访问页面数
                'pages_per_visit': [
                    '[data-automation-id="pages-per-visit"]',
                    '.wa-overview__pages-per-visit',
                    '[class*="pages-per-visit"]'
                ]
            }

            # 尝试提取具体指标
            for metric_name, selectors in specific_selectors.items():
                for selector in selectors:
                    try:
                        elements = page.query_selector_all(selector)
                        for element in elements:
                            text = element.inner_text().strip()
                            if text and text not in ['', '-', 'N/A']:
                                metrics[metric_name] = text
                                print(f"找到 {metric_name}: {text}")
                                break
                        if metric_name in metrics:
                            break
                    except:
                        continue

            # 通用指标提取
            general_selectors = [
                # 数字显示
                '[class*="number"]',
                '[class*="value"]',
                '[class*="metric"]',
                '[class*="kpi"]',
                # 可能的数据容器
                '.wa-overview__engagement-display [class*="value"]',
                '.wa-overview__demographics-display [class*="value"]',
                '[data-automation-id*="metric"]',
                '[data-automation-id*="value"]'
            ]

            for selector in general_selectors:
                try:
                    elements = page.query_selector_all(selector)
                    for i, element in enumerate(elements):
                        try:
                            text = element.inner_text().strip()
                            # 检查是否像是数字指标
                            if text and (
                                any(char.isdigit() for char in text) or
                                '%' in text or
                                'K' in text or
                                'M' in text or
                                'B' in text or
                                ':' in text  # 时间格式
                            ):
                                key = f'general_metric_{i}'
                                if key not in metrics:
                                    metrics[key] = text
                        except:
                            continue
                except:
                    continue

            print(f"总共提取到 {len(metrics)} 个指标")

        except Exception as e:
            print(f"提取指标数据失败: {e}")

        return metrics

    def extract_chart_data(self, page):
        """提取图表数据"""
        charts = {}

        try:
            # 尝试提取图表相关的数据
            chart_selectors = [
                '[data-automation-id="chart"]',
                '.chart-container',
                '.highcharts-container',
                'svg[class*="chart"]'
            ]

            for selector in chart_selectors:
                elements = page.query_selector_all(selector)
                for i, element in enumerate(elements):
                    try:
                        # 获取图表的属性或文本
                        chart_info = {
                            'type': element.get_attribute('class') or 'unknown',
                            'data_attributes': {}
                        }

                        # 获取所有data-*属性
                        for attr in ['data-automation-id', 'data-testid', 'data-chart-type']:
                            value = element.get_attribute(attr)
                            if value:
                                chart_info['data_attributes'][attr] = value

                        charts[f'chart_{i}'] = chart_info
                    except:
                        continue

            print(f"找到 {len(charts)} 个图表")

        except Exception as e:
            print(f"提取图表数据失败: {e}")

        return charts

    def extract_table_data(self, page):
        """提取表格数据"""
        tables = {}

        try:
            # 查找表格
            table_elements = page.query_selector_all('table')

            for i, table in enumerate(table_elements):
                try:
                    rows = table.query_selector_all('tr')
                    table_data = []

                    for row in rows:
                        cells = row.query_selector_all('td, th')
                        row_data = [cell.inner_text().strip() for cell in cells]
                        if any(row_data):  # 只添加非空行
                            table_data.append(row_data)

                    if table_data:
                        tables[f'table_{i}'] = table_data

                except:
                    continue

            print(f"提取到 {len(tables)} 个表格")

        except Exception as e:
            print(f"提取表格数据失败: {e}")

        return tables

    def extract_all_visible_data(self, page):
        """提取页面上所有可见的数据文本"""
        visible_data = {}

        try:
            print("提取所有可见数据...")

            # 获取所有包含数字的文本元素
            script = """
            () => {
                const data = {};
                const elements = document.querySelectorAll('*');
                let index = 0;

                elements.forEach(el => {
                    // 跳过script、style等不可见元素
                    if (['SCRIPT', 'STYLE', 'META', 'LINK'].includes(el.tagName)) return;

                    const text = el.innerText || el.textContent || '';
                    const trimmedText = text.trim();

                    // 只获取包含数字、百分号、或特定关键词的文本
                    if (trimmedText && (
                        /\\d/.test(trimmedText) ||
                        /%/.test(trimmedText) ||
                        /visits?/i.test(trimmedText) ||
                        /traffic/i.test(trimmedText) ||
                        /bounce/i.test(trimmedText) ||
                        /duration/i.test(trimmedText) ||
                        /pages?/i.test(trimmedText) ||
                        /engagement/i.test(trimmedText)
                    )) {
                        // 避免重复和过长的文本
                        if (trimmedText.length < 200 && !data[trimmedText]) {
                            data[`text_${index}`] = {
                                text: trimmedText,
                                tag: el.tagName,
                                className: el.className,
                                id: el.id
                            };
                            index++;
                        }
                    }
                });

                return data;
            }
            """

            visible_data = page.evaluate(script)
            print(f"提取到 {len(visible_data)} 个可见数据项")

        except Exception as e:
            print(f"提取可见数据失败: {e}")

        return visible_data

    def run(self, url, cookie_file=None, save_data=True):
        """
        运行爬虫访问指定URL

        Args:
            url: 要访问的SimilarWeb Pro URL
            cookie_file: cookie文件路径
            save_data: 是否保存提取的数据

        Returns:
            提取的数据字典
        """
        print(f"开始访问 {url}...")

        # 解析URL参数
        params = self.parse_url_params(url)
        print(f"解析的参数: {params}")

        # 提取域名
        domain = params.get('key') or params.get('domain') or (url.split('key=')[-1] if 'key=' in url else None)
        if domain:
            print(f"目标域名: {domain}")

        # 加载cookies
        cookies = self.load_cookies(cookie_file)

        with sync_playwright() as p:
            try:
                # 启动浏览器
                print("正在启动浏览器...")
                browser = p.chromium.launch(
                    headless=self.headless,
                    channel="chrome" if not self.headless else None,  # 在有头模式下使用已安装的Chrome
                    args=[
                        '--no-sandbox',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--disable-dev-shm-usage',
                        '--disable-extensions',
                        '--no-first-run',
                        '--disable-default-apps',
                        '--disable-background-timer-throttling',
                        '--disable-backgrounding-occluded-windows',
                        '--disable-renderer-backgrounding',
                        '--disable-field-trial-config',
                        '--disable-ipc-flooding-protection'
                    ]
                )
                print("浏览器启动成功")

                # 创建浏览器上下文
                context_options = {
                    "viewport": {"width": 1920, "height": 1080},
                    "accept_downloads": True,
                    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "extra_http_headers": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                        "Accept-Encoding": "gzip, deflate, br",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1",
                    }
                }

                context = browser.new_context(**context_options)

                # 添加更强的反检测脚本
                context.add_init_script("""
                    // 移除webdriver属性
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });

                    // 修改plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });

                    // 修改languages
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en'],
                    });

                    // 移除自动化相关属性
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                """)

                # 设置cookies
                if cookies:
                    context.add_cookies(cookies)
                    print("已将cookies加载到浏览器")

                # 创建新页面
                page = context.new_page()

                # 设置超时时间
                page.set_default_timeout(self.timeout)

                # 直接访问目标URL
                print(f"正在加载页面: {url}")
                try:
                    # 使用更宽松的等待策略
                    page.goto(url, wait_until="domcontentloaded", timeout=60000)
                    print(f"页面已加载")
                except Exception as e:
                    print(f"页面加载失败: {e}")
                    # 尝试重新加载
                    print("尝试重新加载...")
                    try:
                        page.goto(url, wait_until="load", timeout=60000)
                        print("重新加载成功")
                    except Exception as e2:
                        print(f"重新加载也失败: {e2}")
                        raise e2

                # 等待页面加载完成
                self.wait_for_page_load(page)

                # 尝试滚动页面以触发更多数据加载
                print("滚动页面以触发数据加载...")
                try:
                    # 滚动到页面底部
                    page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                    time.sleep(3)
                    # 滚动回顶部
                    page.evaluate("window.scrollTo(0, 0)")
                    time.sleep(2)
                    # 再次等待数据加载
                    time.sleep(5)
                except Exception as e:
                    print(f"滚动页面失败: {e}")

                # 截图
                self.take_screenshot(page, f"_{domain or 'page'}")

                # 保存页面内容
                self.save_page_content(page, f"_{domain or 'page'}")

                # 提取数据
                print("开始提取数据...")
                data = self.extract_website_data(page)

                # 保存当前cookies
                self.save_cookies(page)

                # 保存提取的数据
                if save_data and data:
                    self.save_data(data, domain)

                print("数据提取完成")
                return data

            except Exception as e:
                print(f"运行过程中出错: {e}")
                return None
            finally:
                print("浏览器会话已结束")

    def save_data(self, data, domain=None):
        """
        保存提取的数据到JSON文件

        Args:
            data: 提取的数据
            domain: 域名（用于文件名）
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"similarweb_data_{domain or 'unknown'}_{timestamp}.json"
        file_path = os.path.join(os.path.dirname(__file__), 'results', filename)

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            print(f"数据已保存到: {file_path}")
            return file_path
        except Exception as e:
            print(f"保存数据失败: {e}")
            return None


def main():
    """主函数"""
    # 目标URL
    target_url = "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com"

    # 创建爬虫实例
    spider = SimilarWebProSpider(headless=False)

    # 运行爬虫
    data = spider.run(target_url)

    if data:
        print("成功获取数据")
    else:
        print("获取数据失败")


if __name__ == "__main__":
    main()